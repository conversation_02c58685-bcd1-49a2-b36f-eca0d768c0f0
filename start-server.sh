#!/bin/bash

if [[ "$@" == "" ]]; then
        echo "$(basename $0) YEAR" 1>&2
        exit 1
fi

YEAR=$1
mkdir -p logs

export CLASSPATH=/mnt/big/.log4jrc:$(find -iname \*.jar | paste -s -d:)
java -server WikiRankServer \
	-t enwiki.fcl \
	-h rank/enwiki-h.ranks -i rank/enwiki-indegree.ranks -v rank/enwiki-pv.ranks -p rank/enwiki-pr-3.ranks \
	index/enwiki-instanceof index/enwiki-birthplace index/enwiki-cast index/enwiki-citizenship index/enwiki-country index/enwiki-director \
	index/enwiki-gender index/enwiki-genre index/enwiki-language index/enwiki-occupation \
	-P$YEAR
