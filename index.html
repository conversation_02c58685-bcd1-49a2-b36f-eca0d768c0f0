<!DOCTYPE html>
<html lang="en">

<head>
	<title>The Open Wikipedia Ranking</title>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->

	<link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.2.0/css/bootstrap.min.css" rel="stylesheet">
	<link href="https://www.fuelcdn.com/fuelux/3.6.3/css/fuelux.min.css" rel="stylesheet">

	<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.js"></script>
	<script type="text/javascript" src="https://maxcdn.bootstrapcdn.com/bootstrap/3.2.0/js/bootstrap.min.js"></script>
	<script type="text/javascript" src="https://www.fuelcdn.com/fuelux/3.6.3/js/fuelux.min.js"></script>

	<script type="text/javascript" src="jquery.parsequery.min.js"></script>

	<script type="text/javascript" src="jquery.caret.js"></script>
	<script type="text/javascript" src="share-button.js"></script>
	<script type="text/javascript" src="completions.js"></script>
	<script type="text/javascript" src="typeahead.bundle.min.js"></script>

	<link
		href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
		rel="stylesheet">
	<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/hack-font@3/build/web/hack.css">

	<style type="text/css">
		@import "share-button.css";

		body {
			font-family: "Inter", sans-serif;
			padding-top: 70px;
		}

		/* Add a small top margin to the jumbotron for better spacing */
		.jumbotron {
			margin-top: 15px;
		}

		/* Mobile-specific fixes for navbar overlap */
		@media (max-width: 767px) {
			body {
				padding-top: 0;
			}

			.container.theme-showcase {
				margin-top: 70px;
				padding-top: 20px;
			}

			.jumbotron {
				margin-top: 0;
			}
		}

		@media (max-width: 480px) {
			.container.theme-showcase {
				margin-top: 80px;
				padding-top: 25px;
			}

			.jumbotron {
				padding: 30px 15px;
				margin-bottom: 30px;
			}

			.jumbotron h1 {
				font-size: 36px;
			}
		}

		samp,
		code {
			font-family: Hack, monospace;
			font-size: 95%
		}

		.fuelux .repeater-search {
			float: none;
			width: auto;
		}

		.fuelux .repeater-header-left {
			width: 100%;
		}

		.fuelux .twitter-typeahead {
			width: 100%
		}

		.fuelux #lens {
			margin-bottom: 4px
		}

		.fuelux .tt-dropdown-menu {
			background: white
		}

		#share {
			padding-top: 1em;
			padding-bottom: .5em;
		}

		.tt-dropdown-menu {
			max-height: 150px;
			overflow-y: auto;
		}

		.tt-hint {
			width: 100%;
			height: 30px;
			padding: 8px 12px;
			font-size: 24px;
			line-height: 30px;
			border: 2px solid #ccc;
			-webkit-border-radius: 8px;
			-moz-border-radius: 8px;
			border-radius: 8px;
			outline: none;
		}

		.typeahead {
			background-color: #fff;
		}

		.typeahead:focus {
			border: 2px solid #0097cf;
		}

		.tt-query {
			-webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
			-moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
			box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
		}

		.tt-hint {
			color: #999
		}

		.tt-dropdown-menu {
			width: 100%;
			margin-top: 6px;
			padding: 4px;
			background-color: #fff;
			border: 1px solid #ccc;
			border: 1px solid rgba(0, 0, 0, 0.2);
			-webkit-border-radius: 8px;
			-moz-border-radius: 8px;
			border-radius: 8px;
			-webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
			-moz-box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
			box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
		}

		.tt-suggestion {
			padding: 3px 20px;
			line-height: 24px;
		}

		.tt-suggestion.tt-cursor {
			color: #fff;
			background-color: #0097cf;
		}

		.tt-suggestion p {
			margin: 0;
		}
	</style>

	<script>
		var score = false;

		function indexPrefix(index) {
			return index == "instanceof" ? "" : index + ":";
		}

		var matchesPattern = function (a, b) {
			if (b.length > a.length) return false;
			if (b.length < 5) { // prefix
				for (var j = 0; j < b.length; j++)
					if (a.charAt(j) != b.charAt(j)) return false;
				return true;
			}
			else { // substring match
				for (var s = 0; s <= a.length - b.length; s++) {
					var j;
					for (j = b.length; j-- != 0;)
						if (a.charAt(j + s) != b.charAt(j)) break;
					if (j == -1) return true;
				}
				return false;
			}
		}

		var substringMatcher = function (strs) {
			return function findMatches(q, cb) {
				var pos = $('#search').caret();
				var matches, substrRegex;

				// an array that will be populated with substring matches
				matches = [];
				// Determine last token
				var preCaret = q.substring(0, pos);
				var postCaret = q.substring(pos, q.length);
				if (postCaret.length > 0 && postCaret.charAt(0) != ' ') postCaret = " " + postCaret;

				var a = preCaret.split(/[|&\(\)\s]+/g);
				var last = a[a.length - 1];
				var prefix = preCaret.substring(0, preCaret.length - last.length);
				if (last.length != 0) {
					// regex used to determine if a string contains the substring `q`
					var colon = last.lastIndexOf(':');
					if (colon == -1)
						$.each(sugg, function (key, value) {
							for (var i = 0; i < value.length; i++)
								if (matchesPattern(value[i], last)) matches.push({
									value: prefix + indexPrefix(key) + value[i] + postCaret
								});
						});
					else {
						var a = last.split(':');
						var s = sugg[a[0]];
						if (s)
							for (var i = 0; i < s.length; i++)
								if (matchesPattern(s[i], a[1])) matches.push({
									value: prefix + indexPrefix(a[0]) + s[i] + postCaret
								});

					}
				}
				cb(matches);
			};
		};


		var currentList, currentMiniList, currentType = "harmonic",
			currentPageSize = 10,
			currentPage = 0,
			share; // Watch out: must be in sync with clean interface

		var dataSource = function (options, callback) {
			if (options.filter) options.type = options.filter.value; // Replace object with string.
			options.score = score;
			$.getJSON("/Q/", options, function (x) {
				callback(x);
				currentList = x.list;
				currentMiniList = x.miniList;
				currentType = x.type;
				currentPageSize = x.pageSize;
				currentPage = x.page;
				if (x.error) alert(x.error);
			});
			history.replaceState({}, null, makeUrl(null));
		}

		function makeUrl(host) {
			if (host == null) host = window.location.hostname
			return "https://" + host + "?" + $.param({
				search: $('#search').typeahead('val'),
				type: currentType,
				pageSize: currentPageSize
			});
		}

		function makeTitle() {
			var search = $('#search').typeahead('val');
			return search && search.length > 0 ? "WikiRank: " + search : "WikiRank";
		}

		$(document).ready(function () {
			share = new ShareButton({
				networks: {
					pinterest: {
						enabled: false
					},
					linkedin: {
						enabled: false
					},
					reddit: {
						before: function () {
							this.title = makeTitle();
							this.url = makeUrl();
						}
					},
					email: {
						before: function () {
							this.description = makeUrl() + "\n\n" + currentList;
							this.title = makeTitle();
						}
					},
					facebook: {
						app_id: '270493256470273',
						before: function () {
							this.url = makeUrl();
						}
					},
					twitter: {
						before: function () {
							split = currentMiniList.split("\n");
							t = "";
							for (i = 0; i < split.length; i++)
								if (t.length + split[i].length > 240) break;
								else t += split[i] + "\n";
							this.description = t + "\n";
							this.title = makeTitle();
							this.url = makeUrl();
						}
					},
					google_plus: {
						before: function () {
							this.url = makeUrl();
						}
					},
				}
			});

			q = $.parseQuery();
			if (q.score) score = true;
			if (q.search) $('#search')[0].value = q.search; // Here .typeahead('val',-) does not work.

			if (!q.type) q.type = "harmonic";
			$("#centralityType li[data-value=" + q.type + "]").attr("data-selected", "true");
			$("#centralityType li[data-value=" + q.type + "] a").click();

			if (!q.pageSize) q.pageSize = 10;
			$("#pageSize li[data-value=" + q.pageSize + "]").attr("data-selected", "true");
			$("#pageSize li[data-value=" + q.pageSize + "] a").click();

			$('#myRepeater').repeater({
				dataSource: dataSource
			});


			$("#search").typeahead({
				hint: false,
				highlight: true,
				minLength: 3
			}, {
				name: 'states',
				displayKey: 'value',
				source: substringMatcher(sugg)
			});

			/*$('#search').on("typeahead:selected", function(event) {  // We must find a way to avoid double updates when the user presses RETURN
				$('#myRepeater').repeater('render', { pageIncrement: null } );
				});*/
		});

		function listsearch(x) {
			$('#search').typeahead('val', x);
			$('#myRepeater').repeater('render', {
				pageIncrement: null
			});
		}
	</script>
</head>

<body role="document" class="fuelux">
	<div id="fb-root"></div>
	<script>
		window.fbAsyncInit = function () {
			FB.init({
				appId: '270493256470273',
				xfbml: true,
				version: 'v2.3'
			});
		};

		(function (d, s, id) {
			var js, fjs = d.getElementsByTagName(s)[0];
			if (d.getElementById(id)) {
				return;
			}
			js = d.createElement(s);
			js.id = id;
			js.src = "//connect.facebook.net/en_US/sdk.js";
			fjs.parentNode.insertBefore(js, fjs);
		}(document, 'script', 'facebook-jssdk'));
	</script>

	<!-- Fixed navbar -->
	<nav class="navbar navbar-inverse navbar-fixed-top">
		<div class="container">
			<div class="navbar-header">
				<button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar"
					aria-expanded="false" aria-controls="navbar">
					<span class="sr-only">Toggle navigation</span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>
			</div>
			<div id="navbar" class="navbar-collapse collapse">
				<ul class="nav navbar-nav">
					<li class="active"><a href="#">Home</a></li>
					<li><a href="more.html">About</a></li>
					<li><a href="faq.html">FAQ</a></li>
				</ul>
			</div>
			<!--/.nav-collapse -->
		</div>
	</nav>

	<div class="container theme-showcase" role="main">

		<!-- Main jumbotron for a primary marketing message or call to action -->
		<div class="jumbotron">
			<h1>The Open Wikipedia Ranking<sup>2016</sup></h1>
			<p>Here you can browse Wikipedia pages by importance. Choose a category or write a complex query, watch the
				result and share your findings!</p>
			<p>
				<a href="more.html" class="btn btn-primary btn-large">Learn more &raquo;</a>
				<a href="faq.html" class="btn btn-primary btn-large">Caveat emptor &raquo;</a>
				<a href="javascript:window.location = makeUrl('wikirank-2015.di.unimi.it')"
					class="btn btn-primary btn-large">&laquo; 2015</a>
			</p>
			<p>
				<button type="button" onmousedown="javascript:listsearch('human');"
					class="btn btn-sm btn-success">Humans</button>
				<button type="button" onmousedown="javascript:listsearch('band');"
					class="btn btn-sm btn-info">Bands</button>
				<button type="button" onmousedown="javascript:listsearch('city');"
					class="btn btn-sm btn-warning">Cities</button>
				<button type="button" onmousedown="javascript:listsearch('painting');"
					class="btn btn-sm btn-info">Paintings</button>
				<button type="button" onmousedown="javascript:listsearch('gender:female');"
					class="btn btn-sm btn-info">Women</button>
				<button type="button" onmousedown="javascript:listsearch('gender:male');"
					class="btn btn-sm btn-warning">Men</button>
				<button type="button" onmousedown="javascript:listsearch('occupation:singer genre:popular_music');"
					class="btn btn-sm btn-info">Pop singers</button>
				<button type="button" onmousedown="javascript:listsearch('novel');"
					class="btn btn-sm btn-warning">Novels</button>
				<button type="button"
					onmousedown="javascript:listsearch('citizenship:italy | citizenship:kingdom_of_italy | citizenship:republic_of_florence');"
					class="btn btn-sm btn-info">Italians</button>
				<button type="button" onmousedown="javascript:listsearch('food');"
					class="btn btn-sm btn-info">Food</button>
				<button type="button" onmousedown="javascript:listsearch('country');"
					class="btn btn-sm btn-success">Countries</button>
				<button type="button"
					onmousedown="javascript:listsearch('occupation:fashion_designer !occupation:singer');"
					class="btn btn-sm btn-success">Fashion designers</button>
				<button type="button" onmousedown="javascript:listsearch('idea');"
					class="btn btn-sm btn-info">Ideas</button>
				<button type="button" onmousedown="javascript:listsearch('theorem !conspiracy_theory');"
					class="btn btn-sm btn-success">Theorems</button>
				<button type="button" onmousedown="javascript:listsearch('play');"
					class="btn btn-sm btn-warning">Plays</button>
				<button type="button" onmousedown="javascript:listsearch('film ! television_program');"
					class="btn btn-sm btn-warning">Films</button>
				<button type="button" onmousedown="javascript:listsearch('occupation:inventor');"
					class="btn btn-sm btn-success">Inventors</button>
				<button type="button" onmousedown="javascript:listsearch('cast:brad_pitt | cast:angelina_jolie');"
					class="btn btn-sm btn-success">Brangelina</button>
		</div>

		<!-- Fuel UX repeater -->

		<div class="repeater" id="myRepeater">
			<div class="repeater-header">
				<div class="repeater-header-left">
					<div class="repeater-search">
						<div class="search input-group">
							<input type="search" id="search" class="form-control" placeholder="Search"
								autofocus="autofocus" autocapitalize="off" />
							<span class="input-group-btn">
								<button class="btn btn-default" type="button" id="lens">
									<span class="glyphicon glyphicon-search"></span>
									<span class="sr-only">Search</span>
								</button>
							</span>
						</div>
					</div>
				</div>
			</div>
			<div class="repeater-viewport">
				<div class="repeater-canvas"></div>
				<div class="loader repeater-loader"></div>
			</div>
			<div class="repeater-footer">
				<div class="repeater-footer-left">
					<div class="repeater-itemization">
						<span><span class="repeater-start"></span> - <span class="repeater-end"></span> of <span
								class="repeater-count"></span> items</span>
						<div class="btn-group selectlist" data-resize="auto">
							<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
								<span class="selected-label">&nbsp;</span>
								<span class="caret"></span>
								<span class="sr-only">Toggle Dropdown</span>
							</button>
							<ul id="pageSize" class="dropdown-menu" role="menu">
								<li data-value="5"><a href="#">5</a></li>
								<li data-value="10"><a href="#">10</a></li>
								<li data-value="20"><a href="#">20</a></li>
								<li data-value="50" data-foo="bar" data-fizz="buzz"><a href="#">50</a></li>
								<li data-value="100"><a href="#">100</a></li>
							</ul>
							<input class="hidden hidden-field" name="itemsPerPage" readonly="readonly"
								aria-hidden="true" type="text" />
						</div>
						<span>per page</span>
					</div>
				</div>

				<span style="margin-left: 30px">ranked by</span>
				<span class="btn-group selectlist repeater-filters" data-resize="auto">
					<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
						<span class="selected-label">&nbsp;</span>
						<span class="caret"></span>
						<span class="sr-only">Toggle Filters</span>
					</button>
					<ul id="centralityType" class="dropdown-menu" role="type">
						<li data-value="harmonic"><a href="#">Harmonic centrality</a></li>
						<li data-value="indegree"><a href="#">Indegree</a></li>
						<li data-value="pagerank"><a href="#">PageRank</a></li>
						<li data-value="pageviews"><a href="#">Page views</a></li>
						<li data-value="all"><a href="#">All</a></li>
					</ul>
					<input class="hidden hidden-field" name="filterSelection" readonly="readonly" aria-hidden="true"
						type="text" />
				</span>

				<div class="repeater-footer-right">
					<div class="repeater-pagination">
						<button type="button" class="btn btn-default btn-sm repeater-prev">
							<span class="glyphicon glyphicon-chevron-left"></span>
							<span class="sr-only">Previous Page</span>
						</button>
						<label class="page-label" id="myPageLabel">Page</label>
						<div class="repeater-primaryPaging active">
							<div class="input-group input-append dropdown combobox">
								<input type="text" class="form-control" aria-labelledby="myPageLabel">
								<div class="input-group-btn">
									<button type="button" class="btn btn-default dropdown-toggle"
										data-toggle="dropdown">
										<span class="caret"></span>
										<span class="sr-only">Toggle Dropdown</span>
									</button>
									<ul class="dropdown-menu dropdown-menu-right"></ul>
								</div>
							</div>
						</div>
						<input type="text" class="form-control repeater-secondaryPaging" aria-labelledby="myPageLabel">
						<span>of <span class="repeater-pages"></span></span>
						<button type="button" class="btn btn-default btn-sm repeater-next">
							<span class="glyphicon glyphicon-chevron-right"></span>
							<span class="sr-only">Next Page</span>
						</button>
					</div>
				</div>
			</div>
		</div>

		<div style="text-align: center" class="container" id="share">
			<share-button></share-button>
		</div>
		<hr>

		<footer>
			<p>Please send bug reports and comments to our <a href="https://groups.google.com/d/forum/wikirank">mailing
					list</a>. This site was brought to you by the <a href="https://law.di.unimi.it/">Laboratory for Web
					Algorithmics</a> of the <a href="https://www.unimi.it/">Università degli Studi di Milano</a>. It is
				powered by <a href="https://mg4j.di.unimi.it/">MG4J</a>. Computations were carried on hardware kindly
				provided by the
				<a href="https://www.quantware.ups-tlse.fr/FETNADINE/">EU-FET grant NADINE (GA 288956)</a> using <a
					href="https://webgraph.di.unimi.it/">WebGraph</a> and the <a
					href="https://law.di.unimi.it/software/law-docs/index.html">LAW library</a>.
			</p>
			<p>If you find our data useful for your research, please quote <a
					href="https://dl.acm.org/doi/10.1145/3701716.3715510">our paper</a>.
		</footer>

	</div>
	<!-- /container -->
</body>

</html>