<!DOCTYPE html>
<html lang="en">

<head>
	<title>FAQ</title>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<link rel="icon" href="../../favicon.ico">

	<link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.2.0/css/bootstrap.min.css" rel="stylesheet">
	<link href="https://www.fuelcdn.com/fuelux/3.6.3/css/fuelux.min.css" rel="stylesheet">

	<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.js"></script>
	<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.2.0/js/bootstrap.min.js"></script>
	<script src="https://www.fuelcdn.com/fuelux/3.6.3/js/fuelux.min.js"></script>

	<link
		href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
		rel="stylesheet">
	<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/hack-font@3/build/web/hack.css">

	<style type="text/css">
		body {
			font-family: "Inter", sans-serif;
			padding-top: 70px;
		}

		samp,
		code {
			font-family: Hack, monospace;
			font-size: 95%
		}

		h2 {
			border-bottom: 2px solid #555;
			padding-bottom: 10px;
			margin-bottom: 20px;
		}

		h3 {
			border-bottom: 1px solid #777;
			padding-bottom: 5px;
			margin-bottom: 15px;
		}
	</style>
</head>

<body role="document" class="fuelux">

	<!-- Fixed navbar -->
	<nav class="navbar navbar-inverse navbar-fixed-top">
		<div class="container">
			<div class="navbar-header">
				<button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar"
					aria-expanded="false" aria-controls="navbar">
					<span class="sr-only">Toggle navigation</span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
					<span class="icon-bar"></span>
				</button>
			</div>
			<div id="navbar" class="navbar-collapse collapse">
				<ul class="nav navbar-nav">
					<li><a href="index.html">Home</a></li>
					<li><a href="more.html">About</a></li>
					<li class="active"><a href="#">FAQ</a></li>
				</ul>
			</div>
			<!--/.nav-collapse -->
		</div>
	</nav>

	<div class="container theme-showcase" role="main">

		<!-- Main jumbotron for a primary marketing message or call to action -->
		<div class="jumbotron">
			<h1>FAQ</h1>
			<p>Answers to some natural and to some preposterous questions.</p>
		</div>


		<div class="container">
			<!-- Example row of columns -->
			<div class="row">
				<div class="col-md-12">
					<h2>By year</h2>

					<p>Every year we build a ranking, so you can see what has changed. Just press the date button
						and you'll see the same results for a different year. You can see which ideas were central
						in <a
							href="https://wikirank-2015.di.unimi.it/?search=idea&amp;type=all&amp;pageSize=10">2015</a>
						and <a
							href="https://wikirank-2016.di.unimi.it/?search=idea&amp;type=all&amp;pageSize=10">2016</a>,
						for
						example.

				</div>
				<div class="col-md-12">
					<h2>Wikidata is not perfect</h2>

					<p>Wikidata is an ongoing project. Some information is missing, as we detailed <a
							href="https://wikirank-2015.di.unimi.it/faq.html">in
							2015</a> (all bugs reported there are now fixed).


					<p>For example, the <a
							href="https://wikirank-2024.di.unimi.it/?search=theorem+!conspiracy_theory&type=harmonic&pageSize=10">first
							and
							best
							theorem ever</a> is “Conspiracy theory”. This happens because “Conspiracy theory” is an
						instance of
						“reductionism”, which is fine,
						but then Wikidata contains the chain of inclusions “reductionism” ⊆ “simplism” ⊆ “modeling
						assumption” ⊆ “lemma” ⊆
						“theorem”,
						which is utter nonsense because of the inclusion “modeling
						assumption” ⊆ “lemma”.

					<p>Look directly into <a href="https://wikidata.org/">Wikidata</a> to find an explanation of
						puzzling results.
				</div>
				<div class="col-md-12">
					<h2>Wikidata classifications might look weird</h2>

					<p>If you look for <a href="index.html?search=occupation:scientist">scientists</a>, you'll find
						that
						Michael Jackson is among the first twenty! For Wikidata he is a writer, so he is a
						“humanities scholar”, which means he's a scientist, too, as Wikidata uses
						“scientist” in a very broad sense. Better look for <a
							href="index.html?search=occupation:physicist">physicists</a>, even if that category has some
						surprises, too.

					<p>If you try “movie”, you won't get anywhere, as Wikidata uses “film” for
						that purpose. Once again, look directly into <a href="https://wikidata.org/">Wikidata</a>.
				</div>
				<div class="col-md-12">
					<h2>Some results are apparently inexplicable</h2>

					<p>If you look for people who were born in Milan, Italy, using
						<a href="index.html?search=birthplace:milan"><samp>birthplace:milan</samp></a>, you'll find that
						the
						most prominent is <a href="https://en.wikipedia.org/wiki/Thomas_Edison">Thomas Edison</a>, who
						was born indeed in Milan, Ohio, US.
						<a href="index.html?search=birthplace:milan%20citizenship:italy"><samp>birthplace:milan
								citizenship:italy</samp></a> is a better query.

					<p>Most important books are chapters of the Bible.
						This happens because somewhere between 2023 and 2024
						Wikidata decided that every chapter of the bible is a “book
						of the Bible”, which however is a subclass of “book”, and
						parts of the Bible have a very high rank because the Bible
						has a very high rank. We thus switched to “novel”.
					</p>
				</div>
				<div class="col-md-12">
					<h2>Obvious limitations</h2>

					<h3>The English version of Wikipedia is slanted towards the English culture</h3>

					<p>This is quite obvious, but it becomes very evident if you use the link structure to identify
						important pages.

					<h3>Global rankings privilege more global phenomena</h3>

					<p>If someone is world famous at doing something, it will be at the top of any other activity he or
						she performs if the activity involves not-so-famous-people.

					<h3>People have different opinions</h3>

					<p>Are the Beatles a pop band, a rock band, a band, or what else? At the time of this writing, they
						are classified as an “English pop-rock band”, but in principle that might change if
						someone edits Wikidata (indeed, they used to be a “rock band”).

				</div>
				<div class="col-md-12">
					<h2>The full syntax</h2>
					<ul>
						<li>
							<p>When you write a term, it is interpreted as an “instance of” Wikidata
								specification. So “human” selects human beings. For all other categories,
								you must write the category, followed by a colon, followed by the term. If there are
								spaces, they must be replaced with underscores. The current categories are
								“instanceof”, “gender”, “occupation”,
								“language”, “citizenship”, “birthplace”,
								“genre”, “country”, “director”, and
								“cast”.

						<li>
							<p>Spaces should be replaced by underscores, as in <a
									href="index.html?search=director:orson_welles"><samp>director:orson_welles</samp></a>.
						<li>
							<p>You can use the vertical bar to have an OR of specifications, the exclamation mark to
								negate, and group elements with parenthesis. So
								<a
									href="index.html?search=(occupation:chemist%7coccupation:biologist)%20citizenship:russia"><samp>(
										occupation:chemist | occupation:biologist ) citizenship:russia</samp></a> will
								show Russian chemists and biologists.

						<li>
							<p>Negation can be useful to filter out unwanted items. For example, instead of looking for
								films with <a href="index.html?search=film"><samp>film</samp></a> you might want to use
								<a href="index.html?search=film!television_program"><samp>film !
										television_program</samp></a>.
					</ul>
				</div>
				<div class="col-md-12">
					<h2>Download data</h2>
					<p>You can do your own research on the scores: <a href="rank/enwiki-2016-h.txt">harmonic
							centrality</a>,
						<a href="rank/enwiki-2016-indegree.txt">indegree</a>, <a
							href="rank/enwiki-2016-pr-3.txt">PageRank</a> and <a href="rank/enwiki-2016-pv.txt">page
							views</a>.
						And you will need the corresponding <a href="enwiki-2016.titles">list of titles</a> and <a
							href="enwiki-2016.uris">URIs</a>.
						We also distribute a <a href="wikirank.tgz">10-year dataset</a> and a <a
							href="owr.tgz">reproducibility toolkit</a>.

					<p>If you find
						our data useful for your research, please quote <a
							href="https://dl.acm.org/doi/10.1145/3701716.3715510">our paper</a>.
				</div>
			</div>


			<hr>

			<footer>
				<p>Please send bug reports and comments to our <a
						href="https://groups.google.com/d/forum/wikirank">mailing list</a>. This site was brought to you
					by the <a href="https://law.di.unimi.it/">Laboratory for Web Algorithmics</a> of the <a
						href="https://www.unimi.it/">Università degli Studi di Milano</a>. It is powered by <a
						href="https://mg4j.di.unimi.it/">MG4J</a>. Computations were carried on hardware kindly provided
					by the
					<a href="https://www.quantware.ups-tlse.fr/FETNADINE/">EU-FET grant NADINE (GA 288956)</a> using <a
						href="https://webgraph.di.unimi.it/">WebGraph</a> and the <a
						href="https://law.di.unimi.it/software/law-docs/index.html">LAW library</a>.
				</p>
			</footer>

		</div>
		<!-- /container -->
</body>

</html>